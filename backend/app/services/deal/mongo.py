"""
MongoDB Implementation of Deal Service

This module implements the DealServiceInterface using MongoDB as the data store.
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple, Union

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.deal import Deal, DealStatus
from app.models.form import CoreFieldType, Form, FormWithDetails, Question
from app.services.base import BaseService
from app.services.deal.interfaces import DealServiceInterface
from app.services.factory import (
    get_context_block_service,
    get_deal_document_service,
    get_queue_service,
    get_thesis_service,
)

logger = get_logger(__name__)


class DealService(BaseService, DealServiceInterface):
    """MongoDB implementation of DealService."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__(db)
        # self.thesis_service: Optional[ThesisServiceInterface] = None

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        self.context_block_service = await get_context_block_service()
        self.thesis_service = await get_thesis_service()

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    # Core CRUD Operations
    async def create_deal(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        deal_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Deal]:
        """Create a new deal directly."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Prepare deal data
            deal_dict = {
                "org_id": org_id,
                "form_id": form_id,
                "submission_ids": [submission_id],
                "created_by": created_by,
                **(deal_data or {}),
            }

            # Create deal
            deal = Deal(**deal_dict)

            # Add initial timeline event
            deal.add_timeline_event("Deal created")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} directly")

            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                    "submission_id": str(submission_id),
                    "created_by": str(created_by),
                    "deal_data": deal_data,
                },
            )
            return None

    async def create_deal_from_submission(
        self,
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        form: Optional[FormWithDetails] = None,
        submission_data: Optional[Dict[str, Any]] = None,
    ) -> Optional[Deal]:
        """Create a new deal from a form submission."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # # Get form if not provided
            # if not form:
            #     form = await Form.find_one(query={"id": form_id})
            #     if not form:
            #         logger.error(f"Form not found: {form_id}")
            #         return None

            # Get submission data if not provided
            if not submission_data:
                # TODO: Get submission data from submission service
                # For now, we'll assume it's provided
                logger.error("Submission data must be provided")
                return None

            # Extract core fields from submission
            core_fields = await self._extract_core_fields(form, submission_data)  # type: ignore

            # Create deal
            deal = Deal(
                org_id=org_id,  # type: ignore
                form_id=form_id,  # type: ignore
                submission_ids=[submission_id],  # type: ignore
                created_by=created_by,  # type: ignore
                **core_fields,
            )

            # Add initial timeline event
            deal.add_timeline_event("Deal created from submission")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} from submission {submission_id}")

            # Migrate submission files to deal documents
            try:
                doc_service = await get_deal_document_service()
                migrated_docs = await doc_service.migrate_submission_files_to_deal(  # type: ignore
                    deal.id, org_id, [submission_id]
                )
                logger.info(
                    f"Migrated {len(migrated_docs)} documents to deal {deal.id}"
                )
            except Exception as e:
                logger.error(f"Failed to migrate documents for deal {deal.id}: {e}")
                # Don't fail deal creation if document migration fails

            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "form_id": str(form_id),
                    "submission_id": str(submission_id),
                    "org_id": str(org_id),
                    "created_by": str(created_by),
                },
            )
            return None

    async def _extract_core_fields(
        self,
        form: Optional[Union[Form, FormWithDetails]],
        submission_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Extract core fields from submission data based on form questions."""
        core_fields = {
            "company_name": None,
            "stage": None,
            "sector": None,
            "company_website": None,
            "founders": [],
        }
        founders = []

        # Return empty core fields if form is None
        if not form:
            return core_fields

        # Get all questions from form
        questions = []

        for section in form.sections:
            section_questions = await Question.find_many(
                query={"section_id": section.id},  # type: ignore
                sort=[("order", 1)],
            )
            questions.extend(section_questions)
        section_map = {sec.id: sec for sec in form.sections}  # type: ignore

        # Extract core fields from submission
        for question in questions:
            if (
                not question.core_field and section_map[question.section_id].repeatable  # type: ignore
            ):
                continue

            answer = submission_data.get(str(question.id))
            if answer is None:
                continue

            if question.core_field == CoreFieldType.COMPANY_NAME:
                core_fields["company_name"] = str(answer)
            elif question.core_field == CoreFieldType.STAGE:
                core_fields["stage"] = str(answer)
            elif question.core_field == CoreFieldType.SECTOR:
                if question.type == "multi_select":
                    core_fields["sector"] = (
                        answer if isinstance(answer, list) else [str(answer)]
                    )
                else:
                    core_fields["sector"] = str(answer)
            elif question.core_field == CoreFieldType.COMPANY_WEBSITE:
                core_fields["company_website"] = str(answer)

        repeatable_qs = {
            str(q.id): q  # Convert ObjectId to string for dictionary keys
            for q in questions
            if q.core_field and section_map[q.section_id].repeatable  # type: ignore
        }

        logger.info(
            f"🔍 DEBUG: Found {len(repeatable_qs)} repeatable questions with core fields"
        )
        for q_id, q in repeatable_qs.items():
            logger.info(
                f"🔍 DEBUG: Repeatable Q - ID: {q_id}, core_field: {q.core_field}, label: {q.label}"
            )

        logger.info(
            f"🔍 DEBUG: Processing {len(submission_data)} submission data items"
        )
        for key, value in list(submission_data.items())[:10]:  # Show first 10 items
            logger.info(f"🔍 DEBUG: Submission key: '{key}' -> value: {value}")

        for raw_key, raw_val in submission_data.items():
            logger.info(
                f"🔍 DEBUG: Processing raw_key: '{raw_key}', raw_val: {raw_val}"
            )

            if "_" not in raw_key:
                logger.info(f"🔍 DEBUG: Skipping '{raw_key}' - no underscore")
                continue

            q_id_str, idx_str = raw_key.rsplit("_", 1)
            logger.info(
                f"🔍 DEBUG: Split '{raw_key}' -> q_id_str: '{q_id_str}', idx_str: '{idx_str}'"
            )

            if not idx_str.isdigit():
                logger.info(
                    f"🔍 DEBUG: Skipping '{raw_key}' - idx_str '{idx_str}' is not digit"
                )
                continue

            idx = int(idx_str)
            logger.info(
                f"🔍 DEBUG: Looking for question with ID: '{q_id_str}' in repeatable_qs"
            )

            q = repeatable_qs.get(
                q_id_str
            )  # Now this will work - string key in string-keyed dict
            if not q:
                logger.info(
                    f"🔍 DEBUG: Question '{q_id_str}' not found in repeatable_qs"
                )
                logger.info(
                    f"🔍 DEBUG: Available repeatable question IDs: {list(repeatable_qs.keys())}"
                )
                continue

            logger.info(
                f"🔍 DEBUG: Found question '{q_id_str}' with core_field: {q.core_field}"
            )

            # make sure there's a slot for this index
            while len(founders) <= idx:
                founders.append({})
                logger.info(
                    f"🔍 DEBUG: Extended founders array to length {len(founders)}"
                )

            # map to your friendly field names
            if q.core_field == CoreFieldType.FOUNDER_NAME:
                key = "name"
            elif q.core_field == CoreFieldType.FOUNDER_ROLE:
                key = "role"
            elif q.core_field == CoreFieldType.FOUNDER_LINKEDIN:
                key = "linkedin"
            else:
                logger.info(f"🔍 DEBUG: Unknown core_field: {q.core_field}")
                continue

            logger.info(f"🔍 DEBUG: Setting founders[{idx}]['{key}'] = {raw_val}")
            founders[idx][key] = raw_val

        logger.info(f"🔍 DEBUG: Final founders array: {founders}")

        if founders:
            core_fields["founders"] = [f for f in founders if f]
            logger.info(
                f"🔍 DEBUG: Set core_fields['founders'] to: {core_fields['founders']}"
            )
        else:
            logger.info("🔍 DEBUG: No founders found, founders array is empty")

        return core_fields

    async def get_deal(self, deal_id: Union[str, ObjectId]) -> Optional[Deal]:
        """Get a deal by ID."""
        try:
            return await Deal.find_one(query={"_id": deal_id})
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return None

    async def list_deals(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        status: Optional[DealStatus] = None,
        form_id: Optional[Union[str, ObjectId]] = None,
        search: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
        tags: Optional[List[str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
    ) -> Tuple[List[Deal], int]:
        """List deals with optional filtering and pagination."""
        try:
            # Build query
            query: Dict[str, Any] = {"org_id": ObjectId(org_id)}

            if status:
                query["status"] = status
            if form_id:
                query["form_id"] = ObjectId(form_id)
            if search:
                query["company_name"] = {"$regex": search, "$options": "i"}
            if stage:
                query["stage"] = stage
            if sector:
                query["sector"] = {
                    "$in": [sector] if isinstance(sector, str) else sector
                }
            if tags:
                query["tags"] = {"$in": tags}

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(query=query, skip=skip, limit=limit, sort=sort)

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "skip": skip,
                    "limit": limit,
                    "status": status.value if status else None,
                    "form_id": str(form_id) if form_id else None,
                    "search": search,
                    "stage": stage,
                    "sector": sector,
                    "tags": tags,
                    "sort_by": sort_by,
                    "sort_order": sort_order,
                },
            )
            return [], 0

    async def update_deal(
        self, deal_id: Union[str, ObjectId], update_data: Dict[str, Any]
    ) -> Optional[Deal]:
        """Update a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Handle status updates
            if "status" in update_data:
                new_status = DealStatus(update_data["status"])
                notes = update_data.get("status_notes")
                deal.update_status(DealStatus(new_status), notes)
                del update_data["status"]
                if "status_notes" in update_data:
                    del update_data["status_notes"]

            # Handle tag updates
            if "add_tags" in update_data:
                for tag in update_data["add_tags"]:
                    deal.add_tag(tag)
                del update_data["add_tags"]
            if "remove_tags" in update_data:
                for tag in update_data["remove_tags"]:
                    deal.remove_tag(tag)
                del update_data["remove_tags"]

            # Update other fields
            for key, value in update_data.items():
                if hasattr(deal, key):
                    setattr(deal, key, value)

            # Save updates
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            await deal.save(is_update=True)

            return deal
        except Exception as e:
            await self.handle_error(
                e, {"deal_id": str(deal_id), "update_data": update_data}
            )
            return None

    async def delete_deal(self, deal_id: Union[str, ObjectId]) -> bool:
        """Delete a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return False

            # Delete deal
            await deal.delete()
            return True
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return False

    # Auxiliary Operations
    async def get_deals_by_submission(
        self, submission_id: Union[str, ObjectId], org_id: Union[str, ObjectId]
    ) -> List[Deal]:
        """Get deals associated with a specific submission."""
        try:
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {"org_id": org_id, "submission_ids": {"$in": [submission_id]}}

            deals = await Deal.find_many(query=query, sort=[("created_at", -1)])

            return deals
        except Exception as e:
            await self.handle_error(
                e, {"submission_id": str(submission_id), "org_id": str(org_id)}
            )
            return []

    async def get_deals_by_form(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """Get deals associated with a specific form."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {"org_id": org_id, "form_id": form_id}

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(
                query=query, skip=skip, limit=limit, sort=[("created_at", -1)]
            )

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "form_id": str(form_id),
                    "org_id": str(org_id),
                    "skip": skip,
                    "limit": limit,
                },
            )
            return [], 0

    async def get_deal_summary(self, org_id: Union[str, ObjectId]) -> Dict[str, Any]:
        """Get deal summary statistics for dashboard."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Get all deals for the organization
            deals = await Deal.find_many(query={"org_id": org_id})

            # Calculate summary statistics
            total_deals = len(deals)

            # Count by status
            by_status = {}
            for status in DealStatus:
                by_status[status.value] = sum(
                    1 for deal in deals if deal.status == status
                )

            # Count by stage
            by_stage = {}
            for deal in deals:
                if deal.stage:
                    by_stage[deal.stage] = by_stage.get(deal.stage, 0) + 1

            # Count by sector
            by_sector = {}
            for deal in deals:
                if deal.sector:
                    sectors = (
                        deal.sector if isinstance(deal.sector, list) else [deal.sector]
                    )
                    for sector in sectors:
                        by_sector[sector] = by_sector.get(sector, 0) + 1

            # Recent activity (last 7 and 30 days)
            now = datetime.now(timezone.utc)
            seven_days_ago = int((now - timedelta(days=7)).timestamp())
            thirty_days_ago = int((now - timedelta(days=30)).timestamp())

            recent_activity = {
                "last_7_days": sum(
                    1 for deal in deals if deal.created_at >= seven_days_ago
                ),
                "last_30_days": sum(
                    1 for deal in deals if deal.created_at >= thirty_days_ago
                ),
            }

            return {
                "total_deals": total_deals,
                "by_status": by_status,
                "by_stage": by_stage,
                "by_sector": by_sector,
                "recent_activity": recent_activity,
            }
        except Exception as e:
            await self.handle_error(e, {"org_id": str(org_id)})
            return {
                "total_deals": 0,
                "by_status": {},
                "by_stage": {},
                "by_sector": {},
                "recent_activity": {"last_7_days": 0, "last_30_days": 0},
            }

    async def add_timeline_event(
        self,
        deal_id: Union[str, ObjectId],
        event: str,
        notes: Optional[str] = None,
        user_id: Optional[Union[str, ObjectId]] = None,
    ) -> Optional[Deal]:
        """Add a timeline event to a deal."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Add timeline event with user info if provided
            event_data = {
                "date": datetime.now(timezone.utc).isoformat(),
                "event": event,
                "notes": notes,
            }
            if user_id:
                event_data["user_id"] = str(user_id)

            deal.timeline.append(event_data)
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "event": event,
                    "notes": notes,
                    "user_id": str(user_id) if user_id else None,
                },
            )
            return None

    async def update_deal_notes(
        self, deal_id: Union[str, ObjectId], notes: str
    ) -> Optional[Deal]:
        """Update deal notes."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            deal.notes = notes
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())

            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id), "notes": notes})
            return None

    async def bulk_update_deals(
        self,
        deal_ids: List[Union[str, ObjectId]],
        update_data: Dict[str, Any],
        org_id: Union[str, ObjectId],
    ) -> List[Deal]:
        """Bulk update multiple deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            updated_deals = []

            for deal_id in deal_ids:
                if isinstance(deal_id, str):
                    deal_id = ObjectId(deal_id)

                # Get deal and verify it belongs to the organization
                deal = await self.get_deal(deal_id)
                if not deal or deal.org_id != org_id:
                    continue

                # Update the deal
                updated_deal = await self.update_deal(deal_id, update_data)
                if updated_deal:
                    updated_deals.append(updated_deal)

            return updated_deals
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_ids": [str(id) for id in deal_ids],
                    "update_data": update_data,
                    "org_id": str(org_id),
                },
            )
            return []

    async def search_deals(
        self,
        org_id: Union[str, ObjectId],
        query: Optional[str] = None,
        filters: Optional[Dict[str, List[str]]] = None,
        date_range: Optional[Dict[str, str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Deal], int]:
        """Advanced search for deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build base query
            search_query: Dict[str, Any] = {"org_id": org_id}

            # Add text search
            if query:
                search_query["company_name"] = {"$regex": query, "$options": "i"}

            # Add filters
            if filters:
                if "status" in filters:
                    search_query["status"] = {"$in": filters["status"]}
                if "stage" in filters:
                    search_query["stage"] = {"$in": filters["stage"]}
                if "sector" in filters:
                    search_query["sector"] = {"$in": filters["sector"]}
                if "tags" in filters:
                    search_query["tags"] = {"$in": filters["tags"]}

            # Add date range filter
            if date_range:
                date_filter = {}
                if "start" in date_range:
                    start_date = datetime.fromisoformat(date_range["start"]).replace(
                        tzinfo=timezone.utc
                    )
                    date_filter["$gte"] = int(start_date.timestamp())
                if "end" in date_range:
                    end_date = datetime.fromisoformat(date_range["end"]).replace(
                        tzinfo=timezone.utc
                    )
                    date_filter["$lte"] = int(end_date.timestamp())
                if date_filter:
                    search_query["created_at"] = date_filter

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(search_query)

            # Execute search
            deals = await Deal.find_many(
                query=search_query, skip=skip, limit=limit, sort=sort
            )

            return deals, total
        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "query": query,
                    "filters": filters,
                    "date_range": date_range,
                    "sort_by": sort_by,
                    "sort_order": sort_order,
                    "skip": skip,
                    "limit": limit,
                },
            )
            return [], 0

    # PRD 1: Deal Creation with Warm Email Invite
    async def create_deal_with_invite(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        company_name: str,
        invited_email: str,
        company_website: Optional[str] = None,
        pitch_deck_s3_key: Optional[str] = None,
        notes: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
    ) -> Optional[Deal]:
        """
        Create a deal with invite functionality (PRD 1).

        This creates a deal without a submission and sends an invite email
        to the startup with a form link.
        """
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Create deal data
            deal_data = {
                "org_id": org_id,
                "form_id": form_id,
                "submission_ids": [],  # No submission yet
                "created_by": created_by,
                "company_name": company_name,
                "company_website": company_website,
                "invited_email": invited_email,
                "invite_status": "pending",
                "stage": stage,
                "sector": [sector] if sector else None,
                "notes": notes,
                "pitch_deck_url": f"s3://{pitch_deck_s3_key}"
                if pitch_deck_s3_key
                else None,
            }

            # Create deal
            deal = Deal(**deal_data)
            deal.add_timeline_event("Deal created with invite")
            await deal.save()

            logger.info(f"Created deal {deal.id} with invite for {invited_email}")

            # Queue invite email job
            await self._queue_invite_email(deal)

            # Generate initial context block
            # await self._generate_context_block(deal)

            return deal

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "org_id": str(org_id),
                    "form_id": str(form_id),
                    "company_name": company_name,
                    "invited_email": invited_email,
                },
            )
            return None

    async def _queue_invite_email(self, deal: Deal) -> None:
        """Queue an invite email job."""
        try:
            queue_service = await get_queue_service()

            # Queue email job
            await queue_service.enqueue_job(  # type: ignore
                job_type="send_deal_invite_email",
                payload={
                    "deal_id": str(deal.id),
                    "org_id": str(deal.org_id),
                    "form_id": str(deal.form_id),
                    "company_name": deal.company_name,
                    "invited_email": deal.invited_email,
                    "company_website": deal.company_website,
                    "pitch_deck_url": deal.pitch_deck_url,
                },
            )

            logger.info(f"Queued invite email job for deal {deal.id}")

        except Exception as e:
            logger.error(f"Failed to queue invite email for deal {deal.id}: {str(e)}")

    async def _generate_context_block(self, deal: Deal) -> None:
        """Generate and store context block for deal."""
        try:
            # Get context block service if not initialized
            if (
                not hasattr(self, "context_block_service")
                or not self.context_block_service
            ):
                try:
                    self.context_block_service = await get_context_block_service()
                except Exception as e:
                    logger.warning(f"Failed to get context block service: {str(e)}")
                    return

            # Generate context block
            context_url = await self.context_block_service.update_context_block(deal)  # type: ignore

            if context_url:
                logger.info(
                    f"Generated context block for deal {deal.id} at {context_url}"
                )
            else:
                logger.warning(f"Failed to generate context block for deal {deal.id}")

        except Exception as e:
            logger.error(f"Error generating context block for deal {deal.id}: {str(e)}")

    async def update_invite_status(
        self,
        deal_id: Union[str, ObjectId],
        status: str,
        submission_id: Optional[str] = None,
    ) -> Optional[Deal]:
        """Update invite status for a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            update_data = {
                "invite_status": status,
                "updated_at": int(datetime.now(timezone.utc).timestamp()),
            }

            if status == "sent":
                update_data["invite_sent_at"] = int(
                    datetime.now(timezone.utc).timestamp()
                )

            if submission_id:
                # Add submission to deal
                deal = await self.get_deal(deal_id)
                if deal and submission_id not in [
                    str(sid) for sid in deal.submission_ids
                ]:
                    update_data["submission_ids"] = deal.submission_ids + [
                        ObjectId(submission_id)
                    ]

            # Update deal
            await Deal.update_one(
                query={"_id": deal_id},
                update={"$set": update_data},
            )
            deal = await Deal.find_one(query={"_id": deal_id})

            if deal:
                # Add timeline event
                deal.add_timeline_event(f"Invite status updated to: {status}")
                await deal.save(is_update=True)

                # Regenerate context block if submission added
                if submission_id:
                    await self._generate_context_block(deal)

            return deal

        except Exception as e:
            await self.handle_error(
                e,
                {
                    "deal_id": str(deal_id),
                    "status": status,
                    "submission_id": submission_id,
                },
            )
            return None

    async def get_deal_submission_preview(
        self, deal_id: Union[str, ObjectId]
    ) -> Optional[Dict[str, Any]]:
        """
        Get a comprehensive preview of all submissions for a deal.

        Returns structured data with form questions mapped to human-readable labels,
        repeatable sections handled properly, and metadata for frontend rendering.
        """
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get the deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Check if there are any submissions
            if not deal.submission_ids:
                return {
                    "deal_id": str(deal_id),
                    "no_submissions": True,
                    "submissions": [],
                    "dropdown": []
                }

            # Limit to 10 submissions as per requirements
            submission_ids = deal.submission_ids[:10]

            # Get form service
            from app.services.factory import get_form_service
            form_service = await get_form_service()

            submissions_preview = []
            dropdown_options = []

            # Process each submission
            for submission_id in submission_ids:
                try:
                    # Get submission
                    from app.models.form import Submission
                    submission = await Submission.find_one({"_id": ObjectId(submission_id)})
                    if not submission:
                        logger.warning(f"Submission {submission_id} not found")
                        continue

                    # Get form with details
                    form_details = await form_service.get_form_with_details(str(submission.form_id))  # type: ignore
                    if not form_details:
                        logger.warning(f"Form {submission.form_id} not found")
                        continue

                    # Map submission answers to readable format
                    submission_preview = await self._map_submission_to_preview(
                        submission, form_details
                    )

                    if submission_preview:
                        submissions_preview.append(submission_preview)

                        # Add to dropdown options
                        dropdown_options.append({
                            "form_name": form_details.name,
                            "submission_id": str(submission.id),
                            "submitted_at": datetime.fromtimestamp(
                                submission.created_at, tz=timezone.utc
                            ).isoformat()
                        })

                except Exception as e:
                    logger.error(f"Error processing submission {submission_id}: {str(e)}")
                    continue

            return {
                "deal_id": str(deal_id),
                "no_submissions": len(submissions_preview) == 0,
                "submissions": submissions_preview,
                "dropdown": dropdown_options
            }

        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return None

    async def _map_submission_to_preview(
        self, submission, form_details
    ) -> Optional[Dict[str, Any]]:
        """
        Map a submission to preview format with human-readable labels.

        Handles both regular and repeatable sections, mapping answers to display labels.
        """
        try:
            # Extract submission data
            submission_data = submission.answers

            # Handle both old flat format and new structured format
            if isinstance(submission_data, dict):
                # New structured format

                regular_answers = {key: submission_data[key] for key in submission_data if "_" not in key}
                repeatable_answers = {
                    key: submission_data[key] for key in submission_data if "_" in key
                }
                logger.info(f"🔍 DEBUG: Found {len(regular_answers)} regular answers and {len(repeatable_answers)} repeatable answers")
            else:
                # Old flat format - need to separate regular from repeatable
                regular_answers = submission_data
                repeatable_answers = {}

            # Build section map for quick lookup
            section_map = {}
            question_map = {}

            for section in form_details.sections:
                section_id = str(section.id)
                section_map[section_id] = section

                for question in section.questions:
                    question_id = str(question.id)
                    question_map[question_id] = question

            # Process sections in order
            sections_preview = []

            for section in form_details.sections:
                section_id = str(section.id)

                if section.repeatable:
                    # Handle repeatable section
                    section_preview = await self._process_repeatable_section(
                        section, repeatable_answers
                    )
                else:
                    # Handle regular section
                    section_preview = await self._process_regular_section(
                        section, regular_answers
                    )

                if section_preview:
                    sections_preview.append(section_preview)

            return {
                "submission_id": str(submission.id),
                "submitted_at": datetime.fromtimestamp(
                    submission.created_at, tz=timezone.utc
                ).isoformat(),
                "form_name": form_details.name,
                "form_version": form_details.version,
                "sections": sections_preview
            }

        except Exception as e:
            logger.error(f"Error mapping submission to preview: {str(e)}")
            return None

    async def _process_regular_section(
        self, section, answers: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process a regular (non-repeatable) section."""
        try:
            questions_preview = []

            for question in section.questions:
                question_id = str(question.id)

                # Get answer for this question
                answer = answers.get(question_id)
                if answer is None:
                    continue  # Skip questions without answers

                # Map answer to display format
                display_answer = await self._map_answer_to_display(question, answer)

                questions_preview.append({
                    "question_id": question_id,
                    "label": question.label,
                    "type": question.type,
                    "answer": display_answer
                })

            # Only return section if it has answers
            if questions_preview:
                return {
                    "section_id": str(section.id),
                    "title": section.title,
                    "repeatable": False,
                    "questions": questions_preview
                }

            return None

        except Exception as e:
            logger.error(f"Error processing regular section: {str(e)}")
            return None

    async def _process_repeatable_section(
        self, section, repeatable_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process a repeatable section with multiple instances."""
        try:
            instances_preview = []

            # Handle structured repeatable answers
            if repeatable_data:
                # Sort instances by key (should be numeric)
                sorted_instances = sorted(
                    repeatable_data.items(),
                    key=lambda x: int(x.split("_")[1]) if x.split("_")[1].isdigit() else 0
                )
                transformed_instances = {}
                for key, value in sorted_instances:
                    question_id, instance_id = key.split("_", 1)
                    if instance_id not in transformed_instances:
                        transformed_instances[instance_id] = {}
                    transformed_instances[instance_id][question_id] = value

                for instance_id, instance_answers in transformed_instances.items():
                    instance_preview = await self._process_repeatable_instance(
                        section, instance_answers, int(instance_id) if instance_id.isdigit() else 0
                    )
                    if instance_preview:
                        instances_preview.append(instance_preview)

            # Fallback: check for old format answers (question_id__index pattern)
            # if not instances_preview:
            #     instances_preview = await self._extract_instances_from_flat_answers(
            #         section, fallback_answers
            #     )

            # Only return section if it has instances
            if instances_preview:
                return {
                    "section_id": str(section.id),
                    "title": section.title,
                    "repeatable": True,
                    "instances": instances_preview
                }

            return None

        except Exception as e:
            logger.error(f"Error processing repeatable section: {str(e)}")
            return None

    async def _process_repeatable_instance(
        self, section, instance_answers: Dict[str, Any], instance_index: int
    ) -> Optional[Dict[str, Any]]:
        """Process a single instance of a repeatable section."""
        try:
            answers_preview = []

            for question in section.questions:
                question_id = str(question.id)

                # Get answer for this question in this instance
                answer = instance_answers.get(question_id)
                if answer is None:
                    continue  # Skip questions without answers

                # Map answer to display format
                display_answer = await self._map_answer_to_display(question, answer)

                answers_preview.append({
                    "question_id": question_id,
                    "label": question.label,
                    "type": question.type,
                    "answer": display_answer
                })

            # Only return instance if it has answers
            if answers_preview:
                return {
                    "index": instance_index,
                    "answers": answers_preview
                }

            return None

        except Exception as e:
            logger.error(f"Error processing repeatable instance: {str(e)}")
            return None

    async def _extract_instances_from_flat_answers(
        self, section, answers: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract repeatable section instances from flat answer format."""
        try:
            instances_data = {}

            # Look for answers with pattern question_id__index
            for answer_key, answer_value in answers.items():
                if "__" in answer_key:
                    question_id, instance_str = answer_key.split("__", 1)
                    try:
                        instance_index = int(instance_str)

                        # Check if this question belongs to this section
                        question_found = any(
                            str(q.id) == question_id for q in section.questions
                        )

                        if question_found:
                            if instance_index not in instances_data:
                                instances_data[instance_index] = {}
                            instances_data[instance_index][question_id] = answer_value
                    except ValueError:
                        continue  # Skip invalid index format

            # Convert to preview format
            instances_preview = []
            for instance_index in sorted(instances_data.keys()):
                instance_preview = await self._process_repeatable_instance(
                    section, instances_data[instance_index], instance_index
                )
                if instance_preview:
                    instances_preview.append(instance_preview)

            return instances_preview

        except Exception as e:
            logger.error(f"Error extracting instances from flat answers: {str(e)}")
            return []

    async def _map_answer_to_display(self, question, answer) -> Any:
        """
        Map an answer to its display format.

        For select/multi-select questions, maps values to their display labels.
        For other question types, returns the answer as-is.
        """
        try:
            if not answer:
                return answer

            # Handle select and multi-select questions
            if question.type in ["single_select", "multi_select"] and question.options:
                # Create a mapping from value to label
                value_to_label = {}
                for option in question.options:
                    if isinstance(option, dict) and "value" in option and "label" in option:
                        value_to_label[option["value"]] = option["label"]

                if question.type == "single_select":
                    # Single select - return the label for the value
                    return value_to_label.get(answer, answer)
                elif question.type == "multi_select":
                    # Multi select - return list of labels
                    if isinstance(answer, list):
                        return [value_to_label.get(val, val) for val in answer]
                    else:
                        # Handle case where multi-select is stored as single value
                        return value_to_label.get(answer, answer)

            # For all other question types, return answer as-is
            return answer

        except Exception as e:
            logger.error(f"Error mapping answer to display: {str(e)}")
            return answer
