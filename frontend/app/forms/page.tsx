"use client"

import { useEffect, useState } from "react";
import Link from "next/link";
import { Plus, MoreHorizontal, Edit, Eye, Filter, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { DashboardHeader } from "@/components/header";
import { DashboardShell } from "@/components/shell";
import { EmptyPlaceholder } from "@/components/empty-placeholder";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { visualRetreat, mobileRetreat } from "@/lib/utils/responsive";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import FormAPI from "@/lib/api/form-api";
import { Form } from "@/lib/types/form";
import { useAuth } from "@/lib/auth-context";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

export default function FormsPage() {
  const [forms, setForms] = useState<Form[]>([]);
  const [loading, setLoading] = useState(true);
  const { isAuthenticated } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [formToDelete, setFormToDelete] = useState<Form | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [isNavigatingToCreate, setIsNavigatingToCreate] = useState(false);

  // Fetch forms from the API
  useEffect(() => {
    const fetchForms = async () => {
      try {
        console.log("Fetching forms from API");
        setLoading(true);
        const formsList = await FormAPI.listForms();
        console.log("Forms fetched:", formsList);

        // Ensure we always have an array
        if (Array.isArray(formsList)) {
          setForms(formsList);
        } else {
          console.warn("API returned non-array response:", formsList);
          setForms([]);
        }
      } catch (error) {
        console.error("Error fetching forms:", error);
        setForms([]); // Reset to empty array on error
        toast({
          title: "Error",
          description: "Failed to load forms. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (isAuthenticated) {
      fetchForms();
    }
  }, [isAuthenticated]);

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Forms"
        text="Create and manage your forms"
      >
        <Link href="/forms/new">
          <Button 
            disabled={isNavigatingToCreate}
            onClick={() => {
              if (!isNavigatingToCreate) {
                setIsNavigatingToCreate(true);
                // Reset after a delay to allow navigation
                setTimeout(() => setIsNavigatingToCreate(false), 2000);
              }
            }}
          >
            <Plus className="h-4 w-4" />
            {isNavigatingToCreate && <span className="ml-2">Creating...</span>}
          </Button>
        </Link>
      </DashboardHeader>

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Form"
        description={`Are you sure you want to delete the form "${formToDelete?.name || ''}"? This action cannot be undone.`}
        onCancel={() => { setDeleteDialogOpen(false); setFormToDelete(null); }}
        onConfirm={async () => {
          if (!formToDelete) return;
          setDeleting(true);
          try {
            const id = String(formToDelete._id || formToDelete.id);
            await FormAPI.deleteForm(id);
            setForms(forms => forms.filter(f => String(f._id || f.id) !== id));
            toast({
              title: 'Form deleted',
              description: 'The form has been deleted successfully.'
            });
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete form. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
            setFormToDelete(null);
          }
        }}
        loading={deleting}
      />

      <div>
        {loading ? (
          // Loading skeleton
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardHeader className="gap-2">
                  <Skeleton className="h-5 w-1/2" />
                  <Skeleton className="h-4 w-full" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full" />
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-24" />
                </CardFooter>
              </Card>
            ))}
          </div>
        ) : !forms || forms.length === 0 ? (
          <EmptyPlaceholder>
            <EmptyPlaceholder.Icon name="post" />
            <EmptyPlaceholder.Title>No forms created</EmptyPlaceholder.Title>
            <EmptyPlaceholder.Description>
              You don&apos;t have any forms yet. Start creating forms to collect data from your users.
            </EmptyPlaceholder.Description>
            <Link href="/forms/new">
              <Button 
                variant="outline"
                disabled={isNavigatingToCreate}
                onClick={() => {
                  if (!isNavigatingToCreate) {
                    setIsNavigatingToCreate(true);
                    setTimeout(() => setIsNavigatingToCreate(false), 2000);
                  }
                }}
              >
                Create your first form
                {isNavigatingToCreate && <span className="ml-2">...</span>}
              </Button>
            </Link>
          </EmptyPlaceholder>
        ) : (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 auto-rows-fr">
            {forms && Array.isArray(forms) && forms.map((form) => {
              // Ensure we have a valid ID for the form
              const formId = form._id || form.id;

              // Skip forms without valid IDs
              if (!formId) {
                console.warn('Form without valid ID found:', form);
                return null;
              }

              return (
                <Card
                  key={formId}
                  className="hover:shadow-lg transition-shadow flex flex-col h-full"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <CardTitle className="text-xl">{form.name || "Untitled Form"}</CardTitle>
                        <div className="flex items-center gap-3 justify-end">
                          <Badge variant={form.is_active ? 'default' : 'secondary'}>
                            {form.is_active ? 'Active' : 'Draft'}
                          </Badge>
                          <div className="w-full flex justify-end">
                            <span className="text-sm text-muted-foreground text-right block">
                              Updated {form.updated_at ? new Date(form.updated_at).toLocaleDateString() : 'N/A'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem asChild>
                            <Link href={`/forms/${formId}`} className="flex w-full">
                              <Edit className="h-4 w-4 mr-2 text-muted-foreground" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link href={`/preview/${formId}`} className="flex w-full">
                              <Eye className="h-4 w-4 mr-2 text-muted-foreground" />
                              Preview
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => {
                              setFormToDelete(form);
                              setDeleteDialogOpen(true);
                            }}
                            className="text-destructive flex w-full"
                          >
                            <Trash2 className="h-4 w-4 mr-2 text-destructive" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4 flex-grow flex flex-col">
                    <CardDescription className="line-clamp-2">
                      {form.description || "No description provided"}
                    </CardDescription>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Sections:</span>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {form.sections?.length || 0} sections
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {form.sections?.reduce((total, section) => total + (section.questions?.length || 0), 0) || 0} questions
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Status:</span>
                        <span className={`font-medium ${form.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                          {form.is_active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>

                    <div className="pt-2 mt-auto">
                      <Link href={`/forms/${formId}`}>
                        <Button variant="outline" size="sm" className="w-full">
                          <Edit className="h-4 w-4" />
                          Edit Form
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              );
            }).filter(Boolean)}
          </div>
        )}
      </div>
    </DashboardShell>
  );
}
