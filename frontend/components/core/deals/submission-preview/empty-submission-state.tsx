"use client"

import { motion } from "framer-motion"
import { FileText } from "lucide-react"

export function EmptySubmissionState() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-md mx-auto text-center"
    >
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/60 shadow-sm">
        {/* Icon */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="w-16 h-16 mx-auto mb-6 bg-gray-100/80 rounded-2xl flex items-center justify-center"
        >
          <FileText className="w-8 h-8 text-gray-400" />
        </motion.div>

        {/* Content */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No submissions received yet
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            No submissions have been received for this deal yet. 
            Submissions will appear here once they are submitted through the deal form.
          </p>
        </motion.div>

        {/* Decorative elements */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.6 }}
          className="mt-6 flex justify-center space-x-2"
        >
          <div className="w-2 h-2 bg-gray-200 rounded-full" />
          <div className="w-2 h-2 bg-gray-300 rounded-full" />
          <div className="w-2 h-2 bg-gray-200 rounded-full" />
        </motion.div>
      </div>
    </motion.div>
  )
}
